<template>
    <view class="custom-nav">
        <!-- 状态栏占位 -->
        <view class="status-bar" :style="bgColor ? { backgroundColor: bgColor } : {}"></view>
        <!-- 标题栏 -->
        <view class="nav-bar" :style="bgColor ? { backgroundColor: bgColor } : {}">
            <view class="nav-content">
                <!-- 左侧插槽 -->
                <view class="nav-left">
                    <uni-icons type="left" size="24" :color="getIconColor()" v-if="isBack" @click="goBack"></uni-icons>
                    <slot name="left"></slot>
                </view>

                <!-- 中间标题 -->
                <view class="text-secondary nav-center">
                    <slot name="nav-title">{{ title }}</slot>
                </view>

                <!-- 右侧插槽 -->
                <view class="nav-right">
                    <slot name="right"></slot>
                </view>
            </view>
        </view>
        <!-- 占位元素，防止内容被导航栏遮挡 -->
        <view class="placeholder"></view>
    </view>
</template>

<script setup>
import { computed, onMounted, onBeforeUnmount, ref } from 'vue';
import { getTheme, THEME_TYPE } from '@/hooks/useTheme.js';

const props = defineProps({
    title: {
        type: String,
        default: '宝塔'
    },
    isBack: {
        type: Boolean,
        default: false
    },
    bgColor: {
        type: String,
        default: '#20a50a'
    }
});

const currentTheme = ref(getTheme());

// 主题变化监听
function themeChangeHandler(event) {
    currentTheme.value = event.theme;
}

// 组件挂载时添加监听
onMounted(() => {
    uni.$on('themeChange', themeChangeHandler);
});

// 组件销毁前移除监听
onBeforeUnmount(() => {
    uni.$off('themeChange', themeChangeHandler);
});

// 根据主题获取图标颜色
function getIconColor() {
    return props.bgColor 
        ? (props.bgColor === 'transparent' || props.bgColor === 'rgba(0,0,0,0)' ? '#000000' : '#FFFFFF')
        : (currentTheme.value === THEME_TYPE.DARK ? '#FFFFFF' : '#000000');
}

// 返回
function goBack() {
    uni.navigateBack();
}
</script>

<style>
.custom-nav {
    width: 100%;
}

.status-bar {
    height: calc(var(--status-bar-height) + 10rpx);
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 999;
    background: var(--bg-color-secondary);
}

.nav-bar {
    position: fixed;
    top: calc(var(--status-bar-height));
    height: 88rpx;
    width: 100%;
    z-index: 999;
    background: var(--bg-color-secondary);
}

.nav-content {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
}

.nav-left,
.nav-right {
    min-width: 100rpx;
    height: 100%;
    display: flex;
    align-items: center;
}

.nav-left {
    justify-content: flex-start;
}

.nav-right {
    justify-content: flex-end;
}

.nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.nav-title {
    font-size: 34rpx;
    font-weight: 500;
}

.placeholder {
    width: 100%;
    height: calc(var(--status-bar-height) + 88rpx);
}
</style>