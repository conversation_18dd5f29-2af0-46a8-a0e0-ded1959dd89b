import { ref, computed, nextTick } from 'vue';
import {
	getFileList,
	deleteFile,
	setFileName,
	setFilePs,
	createNewFile,
	getDiskInfo,
	getSystemTotal,
	uploadFilesExists,
	uploadFile,
} from '@/api/files';
import { formatTime } from '@/utils';
import { isUndefined } from '@/utils/type';
import { triggerVibrate } from '@/utils/common';
import { $t } from '@/locale/index.js';
import { getAuthenticatedDownloadUrl } from '@/utils/appDownload';
import { PATH, SECRET } from '@/utils/config';
import md5 from 'js-md5';

// 调试工具已移除

// 环境检测工具
export const detectEnvironment = () => {
	const env = {
		isApp: false,
		platform: 'unknown',
		hasPlus: false,
		hasWebview: false,
		systemInfo: null,
		errors: [],
	};

	try {
		// 获取系统信息
		env.systemInfo = uni.getSystemInfoSync();
		env.platform = env.systemInfo.platform || 'unknown';

		// 检测运行环境
		// #ifdef APP-PLUS
		env.isApp = true;
		env.hasPlus = typeof plus !== 'undefined';
		if (env.hasPlus) {
			env.hasWebview = typeof plus.webview !== 'undefined';
		} else {
			env.errors.push('plus对象未定义');
		}
		// #endif

		return env;
	} catch (error) {
		env.errors.push(`环境检测失败: ${error.message}`);
		return env;
	}
};

const DEFAULT_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';
export const currentPath = ref('/');
export const currentDisk = ref('/');
export const dirList = ref([]);
export const fileList = ref([]);
export const paging = ref(null);
export const pageContainer = ref(null);
export const showDeleteDialog = ref(false); // 删除确认弹窗
export const showRenameDialog = ref(false); // 重命名弹窗
export const renameFileName = ref(''); // 重命名文件名
export const showRemarkDialog = ref(false); // 修改备注弹窗
export const renameRemark = ref(''); // 修改备注
export const showCreateDialog = ref(false); // 新建文件夹或文件弹窗
export const createFileType = ref('folder'); // 新建文件类型
export const createFileName = ref(''); // 新建文件名

export const actionList = [
	{
		name: $t('files.newFolder'),
		fontSize: '32rpx',
		color: 'var(--text-color-primary)',
		index: 0,
	},
	{
		name: $t('files.newFile'),
		fontSize: '32rpx',
		color: 'var(--text-color-primary)',
		index: 1,
	},
];
export const actionSheet = ref(null);

// 导航栏下拉菜单状态
export const showNavMenu = ref(false);

// 切换盘符相关状态
export const showDiskSelector = ref(false);
export const diskList = ref([]);

// 切换导航栏下拉菜单
export const toggleNavMenu = () => {
	showNavMenu.value = !showNavMenu.value;
	// 关闭上下文菜单
	if (showContextMenu.value) {
		hideContextMenu();
	}
};

// 隐藏导航菜单
export const hideNavMenu = () => {
	showNavMenu.value = false;
};

// 获取磁盘信息
export const getDiskList = async () => {
	try {
		const res = await getDiskInfo();
		diskList.value = res || [];
		return res;
	} catch (error) {
		return [];
	}
};

// 显示磁盘选择器
export const showDiskSelectorDialog = async () => {
	hideNavMenu();
	await getDiskList();
	showDiskSelector.value = true;
};

// 切换盘符
export const switchDisk = (diskPath) => {
	if (diskPath !== currentDisk.value) {
		currentDisk.value = diskPath;
		currentPath.value = diskPath; // 直接设置为盘符路径
		showDiskSelector.value = false;
		// 刷新文件列表
		if (paging.value) {
			paging.value.reload();
		}
	}
};

// 刷新文件列表
export const handleRefresh = () => {
	hideNavMenu();
	if (paging.value) {
		paging.value.reload();
	}
};

const getFile = (val, type) => {
	const fileArr = val.split(';');
	const fileName = fileArr[0];
	const ext = getExtIcon(fileName, type);
	const time = formatTime(fileArr[2]);
	const permission = fileArr[3];
	const owner = fileArr[4];
	const path = getPath(currentPath.value, fileName);
	const fileMsg = getBtPs(fileName, path);
	return {
		path,
		fileName,
		time,
		permission,
		owner,
		isFile: type !== 'dir',
		type,
		isLink: fileArr[5] || '',
		ext,
		icon: determineFileType(ext), // 文件图标
		size: fileArr[1] || 0,
		ps: fileMsg || fileArr[10] || '',
	};
};

export const getFilesList = async (p = 1, showRow = 100) => {
	dirList.value = [];
	fileList.value = [];
	try {
		const res = await getFileList({
			path: currentPath.value,
			p,
			showRow,
		});
		if (DEFAULT_TYPE === 'domestic') {
			handleDomesticFilesList(res);
		} else {
			handleInternationalFilesList(res);
		}
		return [...dirList.value, ...fileList.value];
	} catch (error) {}
};

const handleDomesticFilesList = async (res) => {
	const { path, dir, files, store, bt_sync, page, tamper_data, search_history, file_recycle } = res;
	let { dirs: tDirs, files: tFiles, msg: pMsg, tip, rules } = tamper_data;
	const isTamperOpen = tip === 'ok'; // 插件防篡改是否开启状态
	const imageNameList = [];
	// 循环目录
	dir.forEach((item, index) => {
		dirList.value.push(reconstructionFile('dir', item, pMsg ? '' : tDirs[index], path, bt_sync));
	});

	// 循环文件
	fileList.value = [];
	files.forEach((item, index) => {
		const file = reconstructionFile('file', item, pMsg ? '' : tFiles[index], path);
		if (file.icon === 'images') {
			imageNameList.push(file.fileName);
		}
		fileList.value.push(file);
	});
};

const handleInternationalFilesList = async (res) => {
	const { PATH: path, DIR: dir, FILES: files } = res;
	dir.forEach((item, index) => {
		dirList.value.push(getFile(item, 'dir'));
	});
	files.forEach((item, index) => {
		fileList.value.push(getFile(item, 'file'));
	});
};

const reconstructionFile = (type, FileItem, TamperData = '0;0', path, bt_sync = []) => {
	const ext = getExtIcon(FileItem.nm, type);
	const fileMsg = getBtPs(FileItem.nm, path);
	const filePath = getPath(path, FileItem.nm);
	const isLock = TamperData.split(';')[0] === '1';
	const tid = TamperData.split(';')[1];
	return {
		icon: determineFileType(ext), // 文件图标
		ext, // 文件后缀
		fileName: FileItem.nm, // 文件名称
		time: formatTime(FileItem.mt), // 时间
		ps: fileMsg || FileItem.rmk || '', // 备注
		size: FileItem.sz, // 文件大小
		type: type, // 文件类型
		isLink: FileItem.lnk, // 软连接
		isShare: Number(FileItem.durl) != 0 ? FileItem.durl : 0, // 是否分享 0否 其它值为分享ID
		isTop: !!FileItem.top, //是否置顶
		isFav: !!Number(FileItem.fav), // 是否收藏
		isNew: false, // 是否新建
		isSync: isSyncType(bt_sync, filePath), // 是否同步
		path: filePath, // 文件路径
		tamperProofId: Number(tid), // 防篡改ID:在防篡改列表中查询
		isFile: type !== 'dir', // 是否是文件
	};
};

const getPath = (path, filename) => {
	return `${path}/${filename}`.replace('//', '/');
};

// 是否同步类型
export const isSyncType = (arr, path) => {
	if (isUndefined(arr) || arr.length == 0) return '';
	const sync = arr.find((item) => item.path === path);
	return sync ? sync.type : '';
};

export const defaultPS = new Map([
	['/etc', 'PS: 系统主要配置文件目录'],
	['/home', 'PS: 用户主目录'],
	['/tmp', 'PS: 公共的临时文件存储点'],
	['/root', 'PS: 系统管理员的主目录'],
	['/home', 'PS: 用户主目录'],
	['/usr', 'PS: 系统应用程序目录'],
	['/boot', 'PS: 系统启动核心目录'],
	['/lib', 'PS: 系统资源文件类库目录'],
	['/mnt', 'PS: 存放临时的映射文件系统'],
	['/www', 'PS: 宝塔面板程序目录'],
	['/bin', 'PS: 存放二进制可执行文件目录'],
	['/dev', 'PS: 存放设备文件目录'],
	['/www/wwwlogs', 'PS: 默认网站日志目录'],
	['/www/server', 'PS: 宝塔软件安装目录'],
	['/www/wwwlogs', 'PS: 网站日志目录'],
	['/www/Recycle_bin', 'PS: 回收站目录,勿动'],
	['/www/server/panel', 'PS: 宝塔主程序目录，勿动'],
	['/www/server/panel/plugin', 'PS: 宝塔插件安装目录'],
	['/www/server/panel/BTPanel', 'PS: 宝塔面板前端文件'],
	['/www/server/panel/BTPanel/static', 'PS: 宝塔面板前端静态文件'],
	['/www/server/panel/BTPanel/templates', 'PS: 宝塔面板前端模板文件'],
]);

export const fileMainType = {
	images: ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'tiff', 'ico', 'JPG', 'webp'],
	compress: ['zip', 'rar', 'gz', 'war', 'tgz', '7z', 'tar.gz', 'tar'],
	video: [
		'mp4',
		'mp3',
		'mpeg',
		'mpg',
		'mov',
		'avi',
		'webm',
		'mkv',
		'mkv',
		'mp3',
		'rmvb',
		'wma',
		'wmv',
		'flac',
		'mov',
	],
	ont_text: [
		'iso',
		'xlsx',
		'xls',
		'doc',
		'docx',
		'tiff',
		'exe',
		'so',
		'bz',
		'dmg',
		'apk',
		'pptx',
		'ppt',
		'xlsb',
		'pdf',
	],
};

// 根据文件类型获取图标类型
export const getFileIconType = (fileType, iconType) => {
	let type = iconType === 'text' || iconType === 'ont_text' ? fileType : iconType;
	switch (type) {
		case 'folder':
			return 'folder-filled';
		case 'images':
			return 'image';
		case 'compress':
			return 'compress';
		case 'video':
			return 'video';
		default:
			return 'file';
	}
};

// 根据文件类型获取图标颜色
export const getFileIconColor = (fileType, iconType) => {
	let type = iconType === 'text' || iconType === 'ont_text' ? fileType : iconType;
	switch (type) {
		case 'folder':
			return '#E6A23C';
		case 'images':
			return '#67C23A';
		case 'compress':
			return '#F56C6C';
		case 'video':
			return '#909399';
		default:
			return '#909399';
	}
};

export const exts = [
	'folder',
	'folder-unempty',
	'sql',
	'c',
	'cpp',
	'cs',
	'flv',
	'css',
	'js',
	'htm',
	'html',
	'java',
	'log',
	'mht',
	'php',
	'url',
	'xml',
	'ai',
	'bmp',
	'cdr',
	'gif',
	'ico',
	'jpeg',
	'jpg',
	'JPG',
	'png',
	'psd',
	'webp',
	'ape',
	'avi',
	'mkv',
	'mov',
	'mp3',
	'mp4',
	'mpeg',
	'mpg',
	'rm',
	'rmvb',
	'swf',
	'wav',
	'webm',
	'wma',
	'wmv',
	'rtf',
	'docx',
	'fdf',
	'potm',
	'pptx',
	'txt',
	'xlsb',
	'xlsx',
	'7z',
	'cab',
	'iso',
	'rar',
	'zip',
	'gz',
	'war',
	'bt',
	'tgz',
	'file',
	'apk',
	'bookfolder',
	'folder-empty',
	'fromchromefolder',
	'documentfolder',
	'fromphonefolder',
	'mix',
	'musicfolder',
	'picturefolder',
	'videofolder',
	'sefolder',
	'access',
	'mdb',
	'accdb',
	'fla',
	'doc',
	'docm',
	'dotx',
	'dotm',
	'dot',
	'pdf',
	'ppt',
	'pptm',
	'pot',
	'xls',
	'csv',
	'xlsm',
	'py',
	'sh',
	'json',
	'lua',
	'bt_split',
	'bt_split_json',
];

const getBtPs = (fileName, path) => {
	let fMsg = '',
		dMsg = defaultPS.get(`${path === '/' ? '/' : `${path}/`}${fileName}`);
	switch (fileName) {
		case '.htaccess':
			fMsg = 'PS: Apache用户配置文件(伪静态)';
			break;
		case 'swap':
			fMsg = 'PS: 宝塔默认设置的SWAP交换分区文件';
			break;
	}
	if (fileName.includes('.upload.tmp')) {
		fMsg = 'PS: 宝塔文件上传临时文件,重新上传从断点续传,可删除';
	}
	if (fileName.includes('.user.ini')) {
		fMsg = 'PS: PHP用户配置文件(防跨站)!';
	}

	if (dMsg) fMsg = dMsg;
	return dMsg || fMsg;
};

/**
 * @description 获取文件类型
 * @param ext 文件后缀
 */
export const determineFileType = (ext) => {
	let returnVal = 'text';
	if (ext) ext = ext.toLowerCase();
	Object.entries(fileMainType).forEach(([key, item]) => {
		item.forEach((items) => {
			if (items == ext) {
				returnVal = key;
			}
		});
	});
	return returnVal;
};

// 获取文件图标
export const getExtIcon = (fileName, type) => {
	if (type === 'dir') return 'folder';
	const extArr = fileName.split('.');
	const extLastName = extArr[extArr.length - 1];
	for (let i = 0; i < exts.length; i++) {
		if (exts[i] == extLastName) {
			return exts[i];
		}
	}
	return 'file';
};

/**
 * 将路径字符串解析为带名称和路径的对象数组
 * @param {string} path - 需要解析的路径，例如'/www/wwwroot'
 * @returns {Array<{name: string, path: string, show: boolean, showFold: boolean, loading: boolean}>} - 解析后的对象数组
 */
export const parsePath = (path) => {
	if (!path)
		return [{ name: currentDisk.value, path: currentDisk.value, show: true, showFold: false, loading: false }];

	// 如果路径就是当前盘符，直接返回盘符名称
	if (path === currentDisk.value) {
		return [{ name: currentDisk.value, path: currentDisk.value, show: true, showFold: false, loading: false }];
	}

	const pathSegments = path.split('/').filter(Boolean);
	const result = [];

	// 第一个元素是盘符
	if (pathSegments.length > 0) {
		const diskPath = '/' + pathSegments[0];
		result.push({ name: diskPath, path: diskPath, show: true, showFold: false, loading: false });

		// 构建累积路径并生成对象数组
		let currentPath = diskPath;
		for (let i = 1; i < pathSegments.length; i++) {
			currentPath += '/' + pathSegments[i];
			result.push({
				name: pathSegments[i],
				path: currentPath,
				show: true,
				showFold: false,
				loading: false,
			});
		}
	} else {
		// 如果是根目录，显示当前盘符
		result.push({ name: currentDisk.value, path: currentDisk.value, show: true, showFold: false, loading: false });
	}

	return result;
};

// 将 pathList 改为计算属性，自动根据 currentPath 计算
export const pathList = computed(() => parsePath(currentPath.value));

export const cutDirPath = async (path) => {
	if (path === '/.Recycle_bin' || path === '/www/.Recycle_bin') {
		pageContainer.value?.notify?.error($t('files.recycleError'));
		return false;
	}
	if (path === '') path = currentDisk.value; // 使用当前盘符而不是根目录
	if (currentPath.value === path) return;
	currentPath.value = path;
	paging.value.reload();
};

/**
 * @description 打开文件
 * @param fileItem 文件信息
 */
export const openFile = (fileItem) => {
	if (fileItem.type === 'dir') {
		cutDirPath(fileItem.path);
	} else {
		// 文件类型判断
		switch (determineFileType(fileItem.ext)) {
			case 'images':
				pageContainer.value?.notify?.error($t('files.imageNotSupported'));
				break;
			case 'compress':
				pageContainer.value?.notify?.error($t('files.compressNotSupported'));
				break;
			case 'video':
				pageContainer.value?.notify?.error($t('files.mediaNotSupported'));
				break;
			default:
				openAceEditor(fileItem);
				break;
		}
	}
};

/**
 *@description   根据路径打开文件编辑器
 * @param {string} path 完整文件路径
 * @param {AnyFunction} callback 回调函数
 */
export const openAceEditor = async (fileItems) => {
	const fileItem = {
		path: fileItems.path,
		title: fileItems.path.split('/').pop(),
		size: fileItems.size,
	};

	if (fileItem?.path?.includes('/dev/pts')) {
		pageContainer.value?.notify?.error($t('files.systemFileError'));
		return;
	}
	if (determineFileType(fileItem.fileName?.split('.').pop()) !== 'text') {
		pageContainer.value?.notify?.error($t('files.fileTypeNotSupported'));
		return;
	}
	// 判断文件大小
	if (fileItem.size > 3 * 1024 * 1024) {
		pageContainer.value?.notify?.warning($t('files.fileSizeTooLarge'));
		return;
	}
	FilesAceEditor(fileItem);
};

export const FilesAceEditor = (fileItem) => {
	uni.navigateTo({
		url: `/linux/files/editor/index?fileItem=${JSON.stringify(fileItem)}`,
		animationType: 'zoom-fade-out',
	});
};

// 长按菜单相关
export const showContextMenu = ref(false);
export const activeFile = ref(null);
export const activeIndex = ref(-1);
export const menuPosition = ref({
	top: '0px',
	left: '0px',
	class: '',
});
export const clonePosition = ref({
	top: '0px',
	left: '0px',
	width: '0px',
	height: '0px',
});

// 触摸状态管理
export const touchStartTime = ref(0);
export const touchStartPos = ref({ x: 0, y: 0 });
export const isTouchMoved = ref(false);
export const longPressTimer = ref(null);
export const LONG_PRESS_THRESHOLD = 600; // 长按阈值，单位毫秒
export const MOVE_THRESHOLD = 10; // 移动阈值，单位像素

// 菜单高度管理
export const actualMenuHeight = ref(140);
export const showTempMenu = ref(false);

// 测量菜单高度的方法
export const measureMenuHeight = () => {
	// 显示临时测量菜单
	showTempMenu.value = true;

	// 等待临时菜单渲染完成
	nextTick(() => {
		uni.createSelectorQuery()
			.select('.temp-measure-menu')
			.boundingClientRect((rect) => {
				if (rect && rect.height > 0) {
					actualMenuHeight.value = rect.height;
				}
				// 隐藏临时菜单
				showTempMenu.value = false;
			})
			.exec();
	});
};

// 触摸处理相关函数
export const handleTouchStart = (event) => {
	// 清除可能存在的定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
	}

	// 记录触摸开始时间和位置
	touchStartTime.value = Date.now();
	touchStartPos.value = {
		x: event.touches[0].clientX,
		y: event.touches[0].clientY,
	};
	isTouchMoved.value = false;

	// 设置长按定时器
	longPressTimer.value = setTimeout(() => {
		if (!isTouchMoved.value) {
			const index = event.currentTarget.dataset.index;
			const fileData = JSON.parse(event.currentTarget.dataset.file);
			showFloatingMenu(fileData, event, index);
		}
	}, LONG_PRESS_THRESHOLD);
};

export const handleTouchMove = (event) => {
	if (!touchStartPos.value) return;

	// 计算移动距离
	const moveX = Math.abs(event.touches[0].clientX - touchStartPos.value.x);
	const moveY = Math.abs(event.touches[0].clientY - touchStartPos.value.y);

	// 如果移动超过阈值，标记为已移动并取消长按定时器
	if (moveX > MOVE_THRESHOLD || moveY > MOVE_THRESHOLD) {
		isTouchMoved.value = true;

		if (longPressTimer.value) {
			clearTimeout(longPressTimer.value);
			longPressTimer.value = null;
		}
	}
};

export const handleTouchEnd = (event) => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}

	// 如果未移动且是短触摸（非长按），则打开文件
	if (!isTouchMoved.value && Date.now() - touchStartTime.value < LONG_PRESS_THRESHOLD) {
		const fileData = JSON.parse(event.currentTarget.dataset.file);
		openFile(fileData);
	}
};

export const handleTouchCancel = () => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}
};

// 显示悬浮菜单 - 两阶段定位
export const showFloatingMenu = (file, event, index) => {
	// 触感反馈
	triggerVibrate();

	activeFile.value = file;
	activeIndex.value = index;

	// 使用传入的事件位置获取精确的触摸点
	const touchX = event.touches[0].clientX;
	const touchY = event.touches[0].clientY;

	// 获取系统信息，用于检测是否会超出屏幕
	const systemInfo = uni.getSystemInfoSync();
	const screenHeight = systemInfo.windowHeight;
	const screenWidth = systemInfo.windowWidth;

	// 获取被长按元素相对于页面的位置
	uni.createSelectorQuery()
		.selectAll('.file-item-container')
		.boundingClientRect((rects) => {
			if (!rects || !rects[index]) return;

			const rect = rects[index];

			// 设置克隆项位置
			clonePosition.value = {
				top: `${rect.top}px`,
				left: `${rect.left}px`,
				width: `${rect.width}px`,
				height: `${rect.height}px`,
			};

			// 预估参数
			const tabbarHeight = 60; // 底部导航栏高度
			const headerHeight = 50; // 顶部标题栏高度
			const menuWidth = 150; // 菜单宽度
			const edgeBuffer = 5; // 边缘安全距离 - 减小以使菜单更贴近克隆项

			// 计算菜单位置
			let menuTop,
				menuLeft,
				menuClass = '';

			// 水平定位 - 居中显示，但保持在屏幕内
			menuLeft = rect.left + rect.width / 2;
			// 防止菜单超出屏幕左侧
			if (menuLeft - menuWidth / 2 < edgeBuffer) {
				menuLeft = menuWidth / 2 + edgeBuffer;
			}
			// 防止菜单超出屏幕右侧
			if (menuLeft + menuWidth / 2 > screenWidth - edgeBuffer) {
				menuLeft = screenWidth - menuWidth / 2 - edgeBuffer;
			}

			// 垂直定位 - 智能判断上方还是下方
			// 计算下方可用空间和上方可用空间
			const spaceBelow = screenHeight - rect.bottom - tabbarHeight;
			const spaceAbove = rect.top - headerHeight;

			// 优先考虑下方显示，如果下方空间不足，再考虑上方
			if (spaceBelow >= actualMenuHeight.value + edgeBuffer) {
				// 下方有足够空间
				menuTop = rect.bottom + edgeBuffer;
				menuClass = 'menu-bottom';
			} else if (spaceAbove >= actualMenuHeight.value + edgeBuffer) {
				// 上方有足够空间 - 菜单底部紧贴克隆项顶部
				menuTop = rect.top - actualMenuHeight.value - 6;
				menuClass = 'menu-top menu-position-bottom'; // 添加菜单位置标记
			} else {
				// 两边都没有理想空间，选择空间较大的一边
				if (spaceBelow >= spaceAbove) {
					// 使用下方剩余空间
					menuTop = rect.bottom + edgeBuffer;
					menuClass = 'menu-bottom';
				} else {
					// 使用上方剩余空间 - 菜单底部紧贴克隆项顶部
					menuTop = rect.top - actualMenuHeight.value;
					menuClass = 'menu-top menu-position-bottom';
				}
			}

			// 设置菜单初始位置和样式
			menuPosition.value = {
				top: `${menuTop}px`,
				left: `${menuLeft}px`,
				class: menuClass,
			};

			// 显示菜单
			showContextMenu.value = true;

			// 第二阶段：在菜单渲染后微调位置
			nextTick(() => {
				// 获取实际菜单高度
				uni.createSelectorQuery()
					.select('.context-menu')
					.boundingClientRect((menuRect) => {
						if (!menuRect) return;

						const actualMenuHeight = menuRect.height;

						// 如果菜单显示在上方，需要向上偏移菜单高度
						if (menuClass.includes('menu-position-bottom')) {
							// 确保菜单底部与克隆项顶部对齐
							const adjustedTop = rect.top - actualMenuHeight;
							menuPosition.value.top = `${adjustedTop}px`;
						}

						// 如果实际菜单宽度与预估不同，调整水平居中
						const actualMenuWidth = menuRect.width;
						if (Math.abs(actualMenuWidth - menuWidth) > 10) {
							// 重新计算水平位置
							let adjustedLeft = rect.left + rect.width / 2;
							if (adjustedLeft - actualMenuWidth / 2 < edgeBuffer) {
								adjustedLeft = actualMenuWidth / 2 + edgeBuffer;
							}
							if (adjustedLeft + actualMenuWidth / 2 > screenWidth - edgeBuffer) {
								adjustedLeft = screenWidth - actualMenuWidth / 2 - edgeBuffer;
							}
							menuPosition.value.left = `${adjustedLeft}px`;
						}
					})
					.exec();
			});
		})
		.exec();
};

// 隐藏悬浮菜单
export const hideContextMenu = () => {
	showContextMenu.value = false;
	activeFile.value = null;
	activeIndex.value = -1;
};

// 文件操作相关方法
// 修改备注
export const confirmRemark = async (close) => {
	try {
		if (activeFile.value) {
			const res = await setFilePs({
				filename: activeFile.value.path,
				ps_type: 0,
				ps_body: renameRemark.value,
			});
			if (res.status) {
				paging.value.reload();
				pageContainer.value?.notify?.success(res.msg);
			} else {
				pageContainer.value?.notify?.error(res.msg);
			}
			renameRemark.value = '';
			close && close();
			hideContextMenu();
		}
	} catch (error) {
	} finally {
		uni.hideLoading();
	}
};

const getChangePath = (path, fileName) => {
	const pathRegex = /^(.*\/)/;
	const match = path.match(pathRegex);
	const fullPath = match ? match[1] : '';
	return `${fullPath}${fileName}`;
};

/**
 * @description 匹配非法字符
 * @param {string} path 配置对象
 * @return 返回匹配结果
 */
export const matchUnqualifiedString = (path) => {
	var containSpecial = RegExp(/[\*\|\:\\\"\/\<\>\?]+/);
	return containSpecial.test(path);
};

// 打开重命名弹窗
export const openRenameDialog = () => {
	if (activeFile.value) {
		renameFileName.value = activeFile.value.fileName;
		showRenameDialog.value = true;
	}
};

// 重命名文件
export const renameFile = async (close) => {
	try {
		if (activeFile.value) {
			if (matchUnqualifiedString(renameFileName.value)) {
				pageContainer.value?.notify?.error($t('files.invalidChars'));
				return;
			}
			if (renameFileName.value === '') {
				pageContainer.value?.notify?.error($t('files.nameEmpty'));
				return;
			}
			if (renameFileName.value === activeFile.value.fileName) {
				pageContainer.value?.notify?.error($t('files.nameNoChange'));
				return;
			}
			uni.showLoading({
				title: $t('files.renaming'),
			});
			const res = await setFileName({
				sfile: getChangePath(activeFile.value.path, activeFile.value.fileName),
				dfile: getChangePath(activeFile.value.path, renameFileName.value),
				rename: true,
			});
			if (res.status) {
				paging.value.reload();
				pageContainer.value?.notify?.success(res.msg);
			} else {
				pageContainer.value?.notify?.error(res.msg);
			}
			renameFileName.value = '';
			close && close();
			hideContextMenu();
		}
	} catch (error) {
	} finally {
		uni.hideLoading();
	}
};

// 确认删除文件
export const confirmDeleteFile = async (close) => {
	if (activeFile.value) {
		uni.showLoading({
			title: $t('files.deleting'),
		});
		const res = await deleteFile({
			path: activeFile.value.path,
			type: activeFile.value.ext,
		});
		if (res.status) {
			paging.value.reload();
			pageContainer.value?.notify?.success(res.msg);
		} else {
			pageContainer.value?.notify?.error(res.msg);
		}
		close && close();
		uni.hideLoading();
		setTimeout(() => {
			hideContextMenu();
		}, 200);
	}
};

// 新建文件夹或文件
export const openCreateFile = (type = 'folder') => {
	hideNavMenu();
	createFileType.value = type;
	showCreateDialog.value = true;
};

// 确认新建文件夹或文件
export const confirmCreate = async (close) => {
	if (createFileName.value === '') {
		pageContainer.value?.notify?.error($t('files.nameEmpty'));
		return;
	}
	if (matchUnqualifiedString(createFileName.value)) {
		pageContainer.value?.notify?.error($t('files.invalidChars'));
		return;
	}
	try {
		uni.showLoading({
			title: $t(createFileType.value === 'folder' ? 'files.creatingFolder' : 'files.creatingFile'),
		});
		const path = `${currentPath.value}/${createFileName.value}`;
		const res = await createNewFile({
			path,
			type: createFileType.value,
		});
		if (res.status) {
			paging.value.reload();
			pageContainer.value?.notify?.success(res.msg);
		} else {
			pageContainer.value?.notify?.error(res.msg);
		}
		close && close();
		createFileName.value = '';
	} catch (error) {
	} finally {
		uni.hideLoading();
	}
};

export const clickActionSheet = () => {
	actionSheet.value.open();
};

export const handleActionSheet = ({ index }) => {
	switch (index) {
		case 0:
			openCreateFile('folder');
			break;
		case 1:
			openCreateFile('file');
			break;
	}
};

// 获取下载链接（用于uni-link组件）
export const getDownloadUrl = async (fileItem) => {
	if (!fileItem) return null;

	// 只允许下载文件，不允许下载文件夹
	if (fileItem.ext === 'folder') {
		return null;
	}

	try {
		// 使用 PATH 配置构建下载链接
		const downloadUrl = `${PATH.value}/download?filename=${encodeURIComponent(fileItem.path)}`;

		// 获取带认证的下载链接
		const authenticatedUrl = await getAuthenticatedDownloadUrl(downloadUrl, fileItem.fileName);
		return authenticatedUrl;
	} catch (error) {
		return null;
	}
};

// 下载文件功能（用于uni-link组件）
export const downloadFileToLocal = async () => {
	if (!activeFile.value) return;

	// 只允许下载文件，不允许下载文件夹
	if (activeFile.value.ext === 'folder') {
		pageContainer.value?.notify?.error('不支持下载文件夹');
		return;
	}

	try {
		// 获取下载链接
		const downloadUrl = await getDownloadUrl(activeFile.value);

		if (downloadUrl) {
			// 这里应该使用uni-link组件来处理下载
			// 在组件中可以直接使用这个链接

			// 隐藏上下文菜单
			hideContextMenu();

			return downloadUrl;
		} else {
			pageContainer.value?.notify?.error('生成下载链接失败');
		}
	} catch (error) {
		pageContainer.value?.notify?.error(error.message || '下载失败');
	}
};

// ==================== 文件上传功能 ====================

// 文件上传相关状态
export const showUploadPopup = ref(false); // 上传相册弹窗
export const showUploadFilePopup = ref(false); // 上传文件弹窗
export const showProgressPopup = ref(false); // 上传进度弹窗
export const saveLocation = ref(''); // 保存位置
export const uploadList = ref([]); // 上传文件列表
export const uploadCount = ref(0); // 当前已上传数量
export const activeCount = ref(0); // 需要上传总数量
export const percentage = ref(0); // 进度条百分比
export const uploadFileInfo = ref({}); // 单个文件信息
export const uploadFileList = ref([]); // 多个文件信息列表
export const newFeatures = ref(false); // 是否有新功能上线
export const uploadOriginalPath = ref(''); // 上传开始时的原始路径，用于保护当前浏览位置

// 检查系统版本并打开上传相册弹窗
export const clickUpload = async () => {
	hideNavMenu();
	try {
		const res = await getSystemTotal();

		if (res.status !== undefined && !res.status) {
			pageContainer.value?.notify?.error(JSON.stringify(res));
			return;
		}

		const versionNum = parseInt(res.version.split('.').join(''));

		if (versionNum >= 745) {
			// 只有当保存位置为空时，才设置为当前路径（避免覆盖用户手动选择的位置）
			if (!saveLocation.value) {
				saveLocation.value = currentPath.value;
			}
			showUploadPopup.value = true;
		} else {
			pageContainer.value?.notify?.error('面板为7.4.5或以上版本才可使用此功能！请升级面板');
		}
	} catch (error) {
		pageContainer.value?.notify?.error('检查系统版本失败');
	}
};

// 打开文件上传弹窗
export const clickFileUpload = () => {
	hideNavMenu();
	// 只有当保存位置为空时，才设置为当前路径（避免覆盖用户手动选择的位置）
	if (!saveLocation.value) {
		saveLocation.value = currentPath.value;
	}
	showUploadFilePopup.value = true;
};

// 选择保存位置
export const chooseSaveLocation = async () => {
	try {
		// 保存当前路径状态，防止被文件选择器影响
		const originalCurrentPath = currentPath.value;

		// 动态导入文件选择器
		const { openFileSelector } = await import('@/stores/fileSelector.js');

		// 打开文件夹选择器（单选模式）
		openFileSelector('folder', false, (selectedPaths) => {
			// 延迟执行回调，确保在文件选择器状态恢复之后执行
			setTimeout(() => {
				// 恢复原始路径，确保当前浏览位置不变
				if (currentPath.value !== originalCurrentPath) {
					currentPath.value = originalCurrentPath;
				}

				if (selectedPaths && selectedPaths.length > 0) {
					const selectedPath = selectedPaths[0];

					// 验证选择的路径不是根目录
					if (selectedPath === '/') {
						pageContainer.value?.notify?.error('请不要选择根目录作为保存位置！');
						return;
					}

					// 更新保存位置（仅更新保存位置，不影响当前浏览路径）
					saveLocation.value = selectedPath;

					// 显示成功提示
					pageContainer.value?.notify?.success(`已选择保存位置: ${selectedPath}`);
				}
			}, 100); // 短暂延迟，确保文件选择器的状态恢复完成
		});
	} catch (error) {
		console.error('打开文件选择器失败:', error);
		pageContainer.value?.notify?.error('打开文件选择器失败');
	}
};

// 打开相册选择图片 - 支持追加模式
export const openUpload = (appendMode = false) => {
	// #ifdef APP-PLUS
	// 计算剩余可选择数量
	const currentCount = uploadList.value.length;
	const maxCount = 9;
	const remainingCount = maxCount - currentCount;

	if (appendMode && remainingCount <= 0) {
		pageContainer.value?.notify?.error(`最多只能选择${maxCount}张图片`);
		return;
	}

	plus.gallery.pick(
		(res) => {
			if (appendMode) {
				// 追加模式：将新选择的文件添加到现有列表
				const newFiles = res.files.slice(0, remainingCount); // 确保不超过最大数量
				uploadList.value = [...uploadList.value, ...newFiles];

				if (res.files.length > remainingCount) {
					pageContainer.value?.notify?.warning(
						`已添加${newFiles.length}张图片，剩余${res.files.length - remainingCount}张因超出限制未添加`,
					);
				} else {
					pageContainer.value?.notify?.success(`已添加${newFiles.length}张图片`);
				}
			} else {
				// 替换模式：完全替换现有列表
				uploadList.value = res.files;
			}
		},
		() => {
			console.log('取消选择照片');
		},
		{
			filter: 'image',
			multiple: true,
			maximum: appendMode ? remainingCount : maxCount,
			system: false,
		},
	);
	// #endif

	// #ifndef APP-PLUS
	pageContainer.value?.notify?.error('此功能仅在APP中可用');
	// #endif
};

// 预览上传的图片
export const previewUpload = (index) => {
	uni.previewImage({
		current: index,
		urls: uploadList.value,
		indicator: 'default',
	});
};

// 删除单个上传图片
export const removeUploadImage = (index) => {
	if (index >= 0 && index < uploadList.value.length) {
		uploadList.value.splice(index, 1);
		pageContainer.value?.notify?.success('已删除图片');
	}
};

// 批量清空所有上传图片
export const clearAllUploadImages = () => {
	if (uploadList.value.length > 0) {
		uploadList.value = [];
		pageContainer.value?.notify?.success('已清空所有图片');
	} else {
		pageContainer.value?.notify?.info('没有可清空的图片');
	}
};

// 添加文件到上传列表
export const addFileToUploadList = (fileInfo) => {
	// 检查文件是否已存在（基于文件名和大小）
	const existingIndex = uploadFileList.value.findIndex(
		(file) => file.name === fileInfo.name && file.size === fileInfo.size,
	);

	if (existingIndex !== -1) {
		pageContainer.value?.notify?.warning('该文件已在上传列表中');
		return;
	}

	// 添加唯一ID用于识别
	const fileWithId = {
		...fileInfo,
		id: Date.now() + Math.random(), // 简单的唯一ID生成
	};

	uploadFileList.value.push(fileWithId);
	pageContainer.value?.notify?.success(`已添加文件: ${fileInfo.name}`);
};

// 删除单个上传文件
export const removeUploadFile = (fileId) => {
	const index = uploadFileList.value.findIndex((file) => file.id === fileId);
	if (index !== -1) {
		const fileName = uploadFileList.value[index].name;
		uploadFileList.value.splice(index, 1);
		pageContainer.value?.notify?.success(`已删除文件: ${fileName}`);
	}
};

// 批量清空所有上传文件
export const clearAllUploadFiles = () => {
	if (uploadFileList.value.length > 0) {
		uploadFileList.value = [];
		pageContainer.value?.notify?.success('已清空所有文件');
	} else {
		pageContainer.value?.notify?.info('没有可清空的文件');
	}
};

// 取消上传相册
export const cancelUpload = (close) => {
	uploadList.value = [];
	showUploadPopup.value = false;
	// 清理保存位置，为下次上传做准备
	saveLocation.value = '';
	if (close) close();
};

// 确认上传相册
export const confirmUpload = async (close) => {
	if (uploadList.value.length <= 0) {
		pageContainer.value?.notify?.error('请先选择图片再上传！');
		return;
	}

	if (saveLocation.value === '/') {
		pageContainer.value?.notify?.error('请不要上传到根目录！');
		return;
	}

	// 保存当前浏览路径，确保上传完成后用户仍在原位置
	uploadOriginalPath.value = currentPath.value;

	percentage.value = 0;
	uploadCount.value = 0;
	activeCount.value = uploadList.value.length;

	// 关闭弹窗并显示进度
	showUploadPopup.value = false;
	showProgressPopup.value = true;
	if (close) close();

	// 延迟开始上传，让进度弹窗先显示
	setTimeout(() => {
		for (let i = 0; i < uploadList.value.length; i++) {
			getImageInfo(uploadList.value[i]);
		}
	}, 700);
};

// 通过文件本地路径获取文件对象及信息
export const getImageInfo = (url) => {
	// #ifdef APP-PLUS
	plus.io.resolveLocalFileSystemURL(
		url,
		(entry) => {
			entry.file((file) => {
				const reader = new plus.io.FileReader();
				reader.readAsDataURL(file);
				reader.onload = function (e) {
					uploadImage(file.name, file.size, e.target.result);
				};
			});
		},
		() => {
			// 如果读取失败，减少总数
			activeCount.value--;
			updateProgress();
		},
	);
	// #endif
};

// 上传单个图片文件
export const uploadImage = async (name, size, base) => {
	try {
		const response = await uploadFile({
			f_path: saveLocation.value,
			f_name: name,
			f_size: size,
			f_start: 0,
			b64_data: base.split(',')[1],
		});

		// 上传成功
		uploadCount.value++;
		percentage.value = Math.round((uploadCount.value / activeCount.value) * 100);

		if (activeCount.value === uploadCount.value) {
			uploadList.value = [];
			showProgressPopup.value = false;
			pageContainer.value?.notify?.success(`共上传[${uploadCount.value}]张照片成功!`);

			// 确保用户仍在原来的浏览位置
			if (uploadOriginalPath.value && currentPath.value !== uploadOriginalPath.value) {
				currentPath.value = uploadOriginalPath.value;
			}

			// 清理保存位置，为下次上传做准备
			saveLocation.value = '';

			// 刷新文件列表
			if (paging.value) {
				paging.value.reload();
			}
		}
	} catch (error) {
		// 如果上传失败就减去这张上传失败的
		activeCount.value--;
		// 重新计算百分比
		if (activeCount.value > 0) {
			percentage.value = Math.round((uploadCount.value / activeCount.value) * 100);
		}

		if (activeCount.value === uploadCount.value) {
			uploadList.value = [];
			showProgressPopup.value = false;
			pageContainer.value?.notify?.success(`共上传[${uploadCount.value}]张照片成功!`);

			// 确保用户仍在原来的浏览位置
			if (uploadOriginalPath.value && currentPath.value !== uploadOriginalPath.value) {
				currentPath.value = uploadOriginalPath.value;
			}

			// 清理保存位置，为下次上传做准备
			saveLocation.value = '';

			// 刷新文件列表
			if (paging.value) {
				paging.value.reload();
			}
		}
	}
};

// 更新上传进度
export const updateProgress = () => {
	if (activeCount.value > 0) {
		percentage.value = Math.round((uploadCount.value / activeCount.value) * 100);
	} else {
		percentage.value = 0;
	}
};

// 单文件上传相关功能
export const uploadMedia = () => {
	// #ifdef APP-PLUS
	try {
		// 尝试多种方式获取当前webview
		let currentWebview;
		try {
			// 方法1：尝试使用getCurrentPages获取当前页面
			const pages = getCurrentPages();
			if (pages && pages.length > 0) {
				const currentPage = pages[pages.length - 1];
				if (currentPage && currentPage.$scope && currentPage.$scope.$getAppWebview) {
					currentWebview = currentPage.$scope.$getAppWebview();
				}
			}
		} catch (e) {
			// 忽略错误，尝试下一种方法
		}

		// 方法2：如果方法1失败，使用plus.webview.currentWebview()
		if (!currentWebview) {
			currentWebview = plus.webview.currentWebview();
		}

		let wv = plus.webview.create('', '/hybrid/html/index.html', {
			'uni-app': 'none', //不加载uni-app渲染层框架，避免样式冲突
			top: 0,
			height: '100%',
			background: 'transparent',
		});

		wv.loadURL('/hybrid/html/index.html');
		currentWebview.append(wv);

		// 使用全局事件监听
		plus.globalEvent.addEventListener('plusMessage', function (msg) {
			if (msg.data.args.data.name == 'postMessage') {
				var infoJson = msg.data.args.data.arg;

				if (infoJson.loading != undefined && infoJson.loading) {
					uni.showLoading({
						title: '读取文件中...',
						mask: true,
					});
				} else {
					uni.hideLoading();
					const fileInfo = {
						name: infoJson.name,
						size: infoJson.size,
						base: infoJson.base,
					};

					// 添加到文件列表
					addFileToUploadList(fileInfo);

					// 同时保持原有的单文件信息（向后兼容）
					uploadFileInfo.value = fileInfo;
					showUploadFilePopup.value = true;
				}
			}
		});

		// 只有当保存位置为空时，才设置为当前路径（避免覆盖用户手动选择的位置）
		if (!saveLocation.value) {
			saveLocation.value = currentPath.value;
		}
	} catch (error) {
		pageContainer.value?.notify?.error('文件选择器创建失败');
	}
	// #endif
};

// 取消文件上传
export const cancelFileUpload = (close) => {
	uploadFileInfo.value = {};
	uploadFileList.value = []; // 清空文件列表
	showUploadFilePopup.value = false;
	// 清理保存位置，为下次上传做准备
	saveLocation.value = '';
	if (close) close();
};

// 确认文件上传
export const confirmFileUpload = async (close) => {
	if (saveLocation.value === '/') {
		pageContainer.value?.notify?.error('请不要上传到根目录！');
		return;
	}

	// 检查是否有文件需要上传
	const filesToUpload =
		uploadFileList.value.length > 0
			? uploadFileList.value
			: uploadFileInfo.value.name
			  ? [uploadFileInfo.value]
			  : [];

	if (filesToUpload.length === 0) {
		pageContainer.value?.notify?.error('请先选择文件！');
		return;
	}

	// 保存当前浏览路径，确保上传完成后用户仍在原位置
	uploadOriginalPath.value = currentPath.value;

	// 如果是多个文件，显示进度弹窗
	if (filesToUpload.length > 1) {
		percentage.value = 0;
		uploadCount.value = 0;
		activeCount.value = filesToUpload.length;
		showUploadFilePopup.value = false;
		showProgressPopup.value = true;
		if (close) close();

		// 延迟开始上传，让进度弹窗先显示
		setTimeout(() => {
			uploadMultipleFiles(filesToUpload);
		}, 700);
	} else {
		// 单个文件上传
		uni.showLoading({
			title: '上传中...',
			mask: true,
		});

		try {
			const fileInfo = filesToUpload[0];
			await uploadFile({
				f_path: saveLocation.value,
				f_name: fileInfo.name,
				f_size: fileInfo.size,
				f_start: 0,
				b64_data: fileInfo.base.split(',')[1],
			});

			uni.hideLoading();
			pageContainer.value?.notify?.success('上传成功');
			showUploadFilePopup.value = false;
			uploadFileInfo.value = {}; // 清空文件信息
			uploadFileList.value = []; // 清空文件列表
			if (close) close();

			// 确保用户仍在原来的浏览位置
			if (uploadOriginalPath.value && currentPath.value !== uploadOriginalPath.value) {
				currentPath.value = uploadOriginalPath.value;
			}

			// 清理保存位置，为下次上传做准备
			saveLocation.value = '';

			// 刷新文件列表
			if (paging.value) {
				paging.value.reload();
			}
		} catch (error) {
			uni.hideLoading();
			pageContainer.value?.notify?.error(error.message || '上传失败');
		}
	}
};

// 批量上传多个文件
export const uploadMultipleFiles = async (filesToUpload) => {
	for (let i = 0; i < filesToUpload.length; i++) {
		try {
			const fileInfo = filesToUpload[i];
			await uploadFile({
				f_path: saveLocation.value,
				f_name: fileInfo.name,
				f_size: fileInfo.size,
				f_start: 0,
				b64_data: fileInfo.base.split(',')[1],
			});

			// 上传成功
			uploadCount.value++;
			percentage.value = Math.round((uploadCount.value / activeCount.value) * 100);

			if (activeCount.value === uploadCount.value) {
				// 所有文件上传完成
				uploadFileList.value = [];
				uploadFileInfo.value = {};
				showProgressPopup.value = false;
				pageContainer.value?.notify?.success(`共上传[${uploadCount.value}]个文件成功!`);

				// 确保用户仍在原来的浏览位置
				if (uploadOriginalPath.value && currentPath.value !== uploadOriginalPath.value) {
					currentPath.value = uploadOriginalPath.value;
				}

				// 清理保存位置，为下次上传做准备
				saveLocation.value = '';

				// 刷新文件列表
				if (paging.value) {
					paging.value.reload();
				}
			}
		} catch (error) {
			// 如果上传失败就减去这个上传失败的文件
			activeCount.value--;
			// 重新计算百分比
			if (activeCount.value > 0) {
				percentage.value = Math.round((uploadCount.value / activeCount.value) * 100);
			}

			if (activeCount.value === uploadCount.value) {
				// 所有文件处理完成
				uploadFileList.value = [];
				uploadFileInfo.value = {};
				showProgressPopup.value = false;
				pageContainer.value?.notify?.success(`共上传[${uploadCount.value}]个文件成功!`);

				// 确保用户仍在原来的浏览位置
				if (uploadOriginalPath.value && currentPath.value !== uploadOriginalPath.value) {
					currentPath.value = uploadOriginalPath.value;
				}

				// 清理保存位置，为下次上传做准备
				saveLocation.value = '';

				// 刷新文件列表
				if (paging.value) {
					paging.value.reload();
				}
			}
		}
	}
};

// 操作表单数据
export const uploadActionList = [
	{
		name: '新建目录',
		index: 0,
	},
	{
		name: '新建空白文件',
		index: 1,
	},
	{
		name: '上传手机相册',
		index: 2,
	},
	{
		name: '上传手机文件',
		index: 3,
	},
];

// 处理操作表单选择
export const handleUploadActionSheet = ({ index }) => {
	switch (index) {
		case 0:
			openCreateFile('folder');
			break;
		case 1:
			openCreateFile('file');
			break;
		case 2:
			clickUpload();
			break;
		case 3:
			clickFileUpload();
			break;
	}
};
