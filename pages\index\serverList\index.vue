<template>
	<page-container ref="pageContainer" :is-show-nav="false" bgColorPage="#E2ECEE">
		<custom-nav :is-back="false" bg-color="#E2ECEE" class="pt-40">
			<template #left>
				<image src="@/static/login/home-logo.png" mode="scaleToFill" class="w-150 h-80 mt-40 mr-20" />
			</template>
			<template #right>
				<view class="mt-40">
					<uv-icon name="plus-circle" color="#909399" size="32"></uv-icon>
				</view>
			</template>
			<template #nav-title>
				<uv-input
					placeholder="请输入服务器或者IP地址"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
					shape="circle"
					border="surround"
					:customStyle="{ backgroundColor: '#fff', height: '60rpx' }"
					class="mt-40"
				></uv-input>
			</template>
		</custom-nav>
		<view class="server-list-container mt-16 px-16">
			<view class="server-list-item mb-16 p-16">
				<view class="server-list-item-title">
					<text>服务器列表</text>
				</view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref } from 'vue';
	import PageContainer from '@/components/pageContainer/index.vue';
	import CustomNav from '@/components/customNav/index.vue';
	import { pageContainer } from './useController';
	import { useConfigStore } from '@/store';
	import { $t } from '@/locale/index.js';
	const { phoneBrand, phoneModel, configList, harmonySslVerification } = useConfigStore().getReactiveState();

	const toggleMoreMenu = () => {
		console.log('toggleMoreMenu');
	};
</script>

<style lang="scss" scoped>
.server-list-item {
	background: var(--bg-color);
	border-radius: 32rpx;
}
</style>
